/**
 * Collection Manager Component
 * Interface for creating, editing, and managing document collections
 */

import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Button,
  TextInput,
  List,
  IconButton,
  Chip,
  Card,
  Divider,
  Menu,
  Portal,
  Modal,
} from 'react-native-paper';
import { useAppDispatch, useAppSelector } from '../../store';
import { createCollection, createTag } from '../../store/slices/documentSlice';
import {
  DocumentCollection,
  DocumentTag,
} from '../../types/document-management';

interface CollectionManagerProps {
  visible: boolean;
  onDismiss: () => void;
  selectedDocuments?: string[];
  onCollectionSelect?: (collectionId: string) => void;
  onTagSelect?: (tagId: string) => void;
}

export const CollectionManager: React.FC<CollectionManagerProps> = ({
  visible,
  onDismiss,
  selectedDocuments: _selectedDocuments = [],
  onCollectionSelect,
  onTagSelect,
}) => {
  const dispatch = useAppDispatch();
  const { collections, tags, isLoading } = useAppSelector(
    state => state.documents,
  );

  const [activeTab, setActiveTab] = useState<'collections' | 'tags'>(
    'collections',
  );
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newItemName, setNewItemName] = useState('');
  const [newItemDescription, setNewItemDescription] = useState('');
  const [newItemColor, setNewItemColor] = useState('#2196F3');
  const [menuVisible, setMenuVisible] = useState<string | null>(null);

  const predefinedColors = [
    '#2196F3',
    '#4CAF50',
    '#FF9800',
    '#F44336',
    '#9C27B0',
    '#607D8B',
    '#795548',
    '#E91E63',
    '#00BCD4',
    '#8BC34A',
  ];

  useEffect(() => {
    if (!visible) {
      setShowCreateForm(false);
      setNewItemName('');
      setNewItemDescription('');
      setNewItemColor('#2196F3');
      setMenuVisible(null);
    }
  }, [visible]);

  const handleCreateCollection = async () => {
    if (!newItemName.trim()) {
      Alert.alert('Error', 'Collection name is required');
      return;
    }

    try {
      await dispatch(
        createCollection({
          name: newItemName.trim(),
          description: newItemDescription.trim() || undefined,
          color: newItemColor,
        }),
      );

      setShowCreateForm(false);
      setNewItemName('');
      setNewItemDescription('');
      setNewItemColor('#2196F3');
    } catch {
      Alert.alert('Error', 'Failed to create collection');
    }
  };

  const handleCreateTag = async () => {
    if (!newItemName.trim()) {
      Alert.alert('Error', 'Tag name is required');
      return;
    }

    try {
      await dispatch(
        createTag({
          name: newItemName.trim(),
          color: newItemColor,
        }),
      );

      setShowCreateForm(false);
      setNewItemName('');
      setNewItemDescription('');
      setNewItemColor('#2196F3');
    } catch {
      Alert.alert('Error', 'Failed to create tag');
    }
  };

  const handleItemSelect = (item: DocumentCollection | DocumentTag) => {
    if (activeTab === 'collections') {
      onCollectionSelect?.(item.id);
    } else {
      onTagSelect?.(item.id);
    }
  };

  const getItemCount = (item: DocumentCollection | DocumentTag): number => {
    return item.documentIds.length;
  };

  const renderCreateForm = () => (
    <Card style={styles.createForm}>
      <Card.Content>
        <Text variant="titleMedium" style={styles.formTitle}>
          Create New {activeTab === 'collections' ? 'Collection' : 'Tag'}
        </Text>

        <TextInput
          label={`${activeTab === 'collections' ? 'Collection' : 'Tag'} Name`}
          value={newItemName}
          onChangeText={setNewItemName}
          style={styles.input}
          mode="outlined"
          autoFocus
        />

        {activeTab === 'collections' && (
          <TextInput
            label="Description (Optional)"
            value={newItemDescription}
            onChangeText={setNewItemDescription}
            style={styles.input}
            mode="outlined"
            multiline
            numberOfLines={2}
          />
        )}

        <Text variant="bodyMedium" style={styles.colorLabel}>
          Color
        </Text>
        <View style={styles.colorPicker}>
          {predefinedColors.map(color => (
            <Button
              key={color}
              mode={newItemColor === color ? 'contained' : 'outlined'}
              onPress={() => setNewItemColor(color)}
              style={[
                styles.colorButton,
                newItemColor === color && {
                  backgroundColor: color,
                },
              ]}
              contentStyle={styles.colorButtonContent}
            >
              <View style={[styles.colorSwatch, { backgroundColor: color }]} />
            </Button>
          ))}
        </View>

        <View style={styles.formActions}>
          <Button
            mode="text"
            onPress={() => setShowCreateForm(false)}
            style={styles.formButton}
          >
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={
              activeTab === 'collections'
                ? handleCreateCollection
                : handleCreateTag
            }
            style={styles.formButton}
            loading={isLoading}
          >
            Create
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  const renderCollectionItem = (collection: DocumentCollection) => (
    <List.Item
      key={collection.id}
      title={collection.name}
      description={
        collection.description || `${getItemCount(collection)} documents`
      }
      left={_props => (
        <View
          style={[
            styles.itemIcon,
            { backgroundColor: collection.color || '#2196F3' },
          ]}
        >
          <Text style={styles.itemIconText}>
            {collection.name.charAt(0).toUpperCase()}
          </Text>
        </View>
      )}
      right={_props => (
        <View style={styles.itemActions}>
          <Chip mode="outlined" compact style={styles.countChip}>
            {getItemCount(collection)}
          </Chip>
          <Menu
            visible={menuVisible === collection.id}
            onDismiss={() => setMenuVisible(null)}
            anchor={
              <IconButton
                icon="dots-vertical"
                size={20}
                onPress={() => setMenuVisible(collection.id)}
              />
            }
          >
            <Menu.Item
              onPress={() => {
                /* TODO: Edit collection */
              }}
              title="Edit"
            />
            <Menu.Item
              onPress={() => {
                /* TODO: Delete collection */
              }}
              title="Delete"
            />
          </Menu>
        </View>
      )}
      onPress={() => handleItemSelect(collection)}
      style={styles.listItem}
    />
  );

  const renderTagItem = (tag: DocumentTag) => (
    <List.Item
      key={tag.id}
      title={tag.name}
      description={`Used in ${getItemCount(tag)} documents`}
      left={_props => (
        <Chip
          mode="outlined"
          style={[styles.tagChip, { borderColor: tag.color || '#757575' }]}
          textStyle={[styles.tagChipText, { color: tag.color || '#757575' }]}
        >
          {tag.name}
        </Chip>
      )}
      right={_props => (
        <View style={styles.itemActions}>
          <Chip mode="outlined" compact style={styles.countChip}>
            {getItemCount(tag)}
          </Chip>
          <Menu
            visible={menuVisible === tag.id}
            onDismiss={() => setMenuVisible(null)}
            anchor={
              <IconButton
                icon="dots-vertical"
                size={20}
                onPress={() => setMenuVisible(tag.id)}
              />
            }
          >
            <Menu.Item
              onPress={() => {
                /* TODO: Edit tag */
              }}
              title="Edit"
            />
            <Menu.Item
              onPress={() => {
                /* TODO: Delete tag */
              }}
              title="Delete"
            />
          </Menu>
        </View>
      )}
      onPress={() => handleItemSelect(tag)}
      style={styles.listItem}
    />
  );

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={styles.modalContainer}
      >
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <Text variant="headlineSmall" style={styles.title}>
              Manage {activeTab === 'collections' ? 'Collections' : 'Tags'}
            </Text>
            <IconButton icon="close" onPress={onDismiss} />
          </View>

          <Divider />

          {/* Tab selector */}
          <View style={styles.tabContainer}>
            <Button
              mode={activeTab === 'collections' ? 'contained' : 'outlined'}
              onPress={() => setActiveTab('collections')}
              style={styles.tabButton}
            >
              Collections ({collections.length})
            </Button>
            <Button
              mode={activeTab === 'tags' ? 'contained' : 'outlined'}
              onPress={() => setActiveTab('tags')}
              style={styles.tabButton}
            >
              Tags ({tags.length})
            </Button>
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
          >
            {showCreateForm && renderCreateForm()}

            {/* Items list */}
            <View style={styles.itemsList}>
              {activeTab === 'collections' ? (
                collections.length > 0 ? (
                  collections.map(renderCollectionItem)
                ) : (
                  <View style={styles.emptyState}>
                    <Text variant="bodyLarge" style={styles.emptyText}>
                      No collections yet
                    </Text>
                    <Text variant="bodyMedium" style={styles.emptySubtext}>
                      Create your first collection to organize your documents
                    </Text>
                  </View>
                )
              ) : tags.length > 0 ? (
                tags.map(renderTagItem)
              ) : (
                <View style={styles.emptyState}>
                  <Text variant="bodyLarge" style={styles.emptyText}>
                    No tags yet
                  </Text>
                  <Text variant="bodyMedium" style={styles.emptySubtext}>
                    Create your first tag to categorize your documents
                  </Text>
                </View>
              )}
            </View>
          </ScrollView>

          {/* Footer */}
          <Divider />
          <View style={styles.footer}>
            <Button mode="text" onPress={onDismiss}>
              Close
            </Button>
            <Button
              mode="contained"
              onPress={() => setShowCreateForm(true)}
              icon="plus"
              disabled={showCreateForm}
            >
              Create {activeTab === 'collections' ? 'Collection' : 'Tag'}
            </Button>
          </View>
        </View>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 8,
    maxHeight: '80%',
  },
  container: {
    maxHeight: '100%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  title: {
    fontWeight: '600',
    color: '#212121',
  },
  tabContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 8,
  },
  tabButton: {
    flex: 1,
  },
  content: {
    maxHeight: 400,
  },
  createForm: {
    margin: 16,
    marginBottom: 8,
  },
  formTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
  input: {
    marginBottom: 12,
  },
  colorLabel: {
    marginBottom: 8,
    color: '#757575',
  },
  colorPicker: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  colorButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    padding: 0,
    margin: 0,
  },
  colorButtonContent: {
    width: 40,
    height: 40,
    margin: 0,
    padding: 0,
  },
  colorSwatch: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  formActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
  },
  formButton: {
    minWidth: 80,
  },
  itemsList: {
    paddingHorizontal: 8,
  },
  listItem: {
    paddingVertical: 8,
  },
  itemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  itemIconText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  itemActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  countChip: {
    marginRight: 8,
  },
  tagChip: {
    marginRight: 8,
  },
  tagChipText: {
    fontSize: 12,
  },
  emptyState: {
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 8,
    color: '#424242',
  },
  emptySubtext: {
    textAlign: 'center',
    color: '#757575',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
});
