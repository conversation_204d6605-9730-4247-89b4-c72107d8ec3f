/**
 * Logging Service
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// Log levels
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4,
}

// Type for log data - can be any serializable value
export type LogData =
  | string
  | number
  | boolean
  | null
  | undefined
  | Record<string, unknown>
  | Array<unknown>
  | Error;

// Log entry interface
export interface LogEntry {
  id: string;
  timestamp: string;
  level: LogLevel;
  message: string;
  category: string;
  data?: LogData;
  stackTrace?: string;
  userId?: string;
  sessionId: string;
}

// Logger configuration
export interface LoggerConfig {
  minLevel: LogLevel;
  maxEntries: number;
  enableConsole: boolean;
  enableStorage: boolean;
  enableCrashReporting: boolean;
  categories: string[];
}

// Default configuration
const DEFAULT_CONFIG: LoggerConfig = {
  minLevel: __DEV__ ? LogLevel.DEBUG : LogLevel.INFO,
  maxEntries: 1000,
  enableConsole: __DEV__,
  enableStorage: true,
  enableCrashReporting: false, // Privacy-first: disabled by default
  categories: [
    'app',
    'navigation',
    'storage',
    'handwriting',
    'documents',
    'notes',
  ],
};

class LoggerService {
  private config: LoggerConfig;
  private sessionId: string;
  private logQueue: LogEntry[] = [];
  private isInitialized = false;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.sessionId = this.generateSessionId();
  }

  // Initialize the logger
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Load existing logs from storage
      if (this.config.enableStorage) {
        await this.loadLogsFromStorage();
      }

      this.isInitialized = true;
      this.info('Logger initialized', 'app', { sessionId: this.sessionId });
    } catch (error) {
      console.error('Failed to initialize logger:', error);
    }
  }

  // Log methods for different levels
  debug(message: string, category = 'app', data?: LogData): void {
    this.log(LogLevel.DEBUG, message, category, data);
  }

  info(message: string, category = 'app', data?: LogData): void {
    this.log(LogLevel.INFO, message, category, data);
  }

  warn(message: string, category = 'app', data?: LogData): void {
    this.log(LogLevel.WARN, message, category, data);
  }

  error(message: string, category = 'app', data?: LogData, error?: Error): void {
    const stackTrace = error?.stack;
    this.log(LogLevel.ERROR, message, category, data, stackTrace);
  }

  fatal(message: string, category = 'app', data?: LogData, error?: Error): void {
    const stackTrace = error?.stack;
    this.log(LogLevel.FATAL, message, category, data, stackTrace);
  }

  // Core logging method
  private log(
    level: LogLevel,
    message: string,
    category: string,
    data?: LogData,
    stackTrace?: string,
  ): void {
    // Check if logging is enabled for this level
    if (level < this.config.minLevel) return;

    // Create log entry
    const logEntry: LogEntry = {
      id: this.generateLogId(),
      timestamp: new Date().toISOString(),
      level,
      message,
      category,
      data,
      stackTrace,
      sessionId: this.sessionId,
    };

    // Add to queue
    this.logQueue.push(logEntry);

    // Maintain max entries limit
    if (this.logQueue.length > this.config.maxEntries) {
      this.logQueue.shift();
    }

    // Console output
    if (this.config.enableConsole) {
      this.logToConsole(logEntry);
    }

    // Persist to storage
    if (this.config.enableStorage) {
      this.persistLogs();
    }

    // Handle fatal errors
    if (level === LogLevel.FATAL && this.config.enableCrashReporting) {
      this.handleFatalError(logEntry);
    }
  }

  // Console logging with appropriate methods
  private logToConsole(entry: LogEntry): void {
    const prefix = `[${entry.category}] ${entry.message}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(prefix, entry.data);
        break;
      case LogLevel.INFO:
        console.info(prefix, entry.data);
        break;
      case LogLevel.WARN:
        console.warn(prefix, entry.data);
        break;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(prefix, entry.data, entry.stackTrace);
        break;
    }
  }

  // Persist logs to AsyncStorage
  private async persistLogs(): Promise<void> {
    try {
      const logsToStore = this.logQueue.slice(-100); // Store last 100 entries
      await AsyncStorage.setItem('inksight_logs', JSON.stringify(logsToStore));
    } catch (error) {
      console.error('Failed to persist logs:', error);
    }
  }

  // Load logs from AsyncStorage
  private async loadLogsFromStorage(): Promise<void> {
    try {
      const storedLogs = await AsyncStorage.getItem('inksight_logs');
      if (storedLogs) {
        const logs: LogEntry[] = JSON.parse(storedLogs);
        this.logQueue = logs;
      }
    } catch (error) {
      console.error('Failed to load logs from storage:', error);
    }
  }

  // Handle fatal errors
  private handleFatalError(entry: LogEntry): void {
    // In a privacy-first app, we don't send data to external services
    // Instead, we prepare the error for local analysis or user-initiated reporting
    console.error('FATAL ERROR:', entry);

    // Could trigger a local error report that user can choose to share
    // this.prepareCrashReport(entry);
  }

  // Get logs for debugging or user-initiated reporting
  getLogs(level?: LogLevel, category?: string, limit = 100): LogEntry[] {
    let filteredLogs = this.logQueue;

    if (level !== undefined) {
      filteredLogs = filteredLogs.filter(log => log.level >= level);
    }

    if (category) {
      filteredLogs = filteredLogs.filter(log => log.category === category);
    }

    return filteredLogs.slice(-limit);
  }

  // Clear logs
  async clearLogs(): Promise<void> {
    this.logQueue = [];
    if (this.config.enableStorage) {
      await AsyncStorage.removeItem('inksight_logs');
    }
    this.info('Logs cleared', 'app');
  }

  // Export logs for user-initiated sharing
  exportLogs(): string {
    return JSON.stringify(this.logQueue, null, 2);
  }

  // Update configuration
  updateConfig(newConfig: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.info('Logger configuration updated', 'app', newConfig);
  }

  // Generate unique session ID
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Generate unique log ID
  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Get current session ID
  getSessionId(): string {
    return this.sessionId;
  }

  // Get logger statistics
  getStats(): {
    totalLogs: number;
    logsByLevel: Record<LogLevel, number>;
    logsByCategory: Record<string, number>;
    sessionId: string;
  } {
    const logsByLevel = this.logQueue.reduce((acc, log) => {
      acc[log.level] = (acc[log.level] || 0) + 1;
      return acc;
    }, {} as Record<LogLevel, number>);

    const logsByCategory = this.logQueue.reduce((acc, log) => {
      acc[log.category] = (acc[log.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalLogs: this.logQueue.length,
      logsByLevel,
      logsByCategory,
      sessionId: this.sessionId,
    };
  }
}

// Create and export singleton instance
export const Logger = new LoggerService();

// Export convenience functions
export const log = {
  debug: (message: string, category?: string, data?: LogData) =>
    Logger.debug(message, category, data),
  info: (message: string, category?: string, data?: LogData) =>
    Logger.info(message, category, data),
  warn: (message: string, category?: string, data?: LogData) =>
    Logger.warn(message, category, data),
  error: (message: string, category?: string, data?: LogData, error?: Error) =>
    Logger.error(message, category, data, error),
  fatal: (message: string, category?: string, data?: LogData, error?: Error) =>
    Logger.fatal(message, category, data, error),
};

export default Logger;
