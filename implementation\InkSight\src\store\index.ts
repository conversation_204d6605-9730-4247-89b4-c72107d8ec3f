/**
 * Redux Store Configuration
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import { configureStore, combineReducers, AnyAction } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// Import reducers
import settingsReducer from './slices/settingsSlice';
import securityReducer from './slices/securitySlice';
import storageReducer from './slices/storageSlice';
import documentReducer from './slices/documentSlice';
// import notesReducer from './slices/notesSlice';
// import uiReducer from './slices/uiSlice';

// Root reducer combining all feature slices
const rootReducer = combineReducers({
  settings: settingsReducer,
  security: securityReducer,
  storage: storageReducer,
  documents: documentReducer,
  // notes: notesReducer,
  // ui: uiReducer,
});

// Persist configuration for offline-first architecture
const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['settings', 'security', 'storage', 'documents'], // Only persist these slices
  blacklist: ['ui'], // Don't persist UI state
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store with Redux Toolkit
export const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: __DEV__, // Enable Redux DevTools in development
});

// Create persistor for Redux Persist
export const persistor = persistStore(store);

// Export types for TypeScript support
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Typed hooks for React components
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Store utilities
export const getStoreState = () => store.getState();
export const dispatchAction = (action: AnyAction) => store.dispatch(action);
