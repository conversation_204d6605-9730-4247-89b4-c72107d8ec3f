/**
 * Document Grid Component
 * Displays documents in a grid layout with Material Design 3 cards
 */

import React from 'react';
import { StyleSheet, Dimensions, RefreshControlProps } from 'react-native';
import { FlatGrid } from 'react-native-super-grid';
import { DocumentLibraryItem } from '../../types/document-management';
import { DocumentCard } from './DocumentCard';

interface DocumentGridProps {
  documents: DocumentLibraryItem[];
  onDocumentPress: (documentId: string) => void;
  onDocumentLongPress: (documentId: string) => void;
  refreshControl?: React.ReactElement<RefreshControlProps>;
  selectedDocuments: string[];
  numColumns?: number;
}

export const DocumentGrid: React.FC<DocumentGridProps> = ({
  documents,
  onDocumentPress,
  onDocumentLongPress,
  refreshControl,
  selectedDocuments,
  numColumns = 2,
}) => {
  const screenWidth = Dimensions.get('window').width;
  const itemSpacing = 16;
  const horizontalPadding = 16;
  const itemWidth =
    (screenWidth - horizontalPadding * 2 - itemSpacing * (numColumns - 1)) /
    numColumns;

  const renderDocument = ({ item }: { item: DocumentLibraryItem }) => (
    <DocumentCard
      document={item}
      onPress={() => onDocumentPress(item.id)}
      onLongPress={() => onDocumentLongPress(item.id)}
      isSelected={selectedDocuments.includes(item.id)}
      width={itemWidth}
    />
  );

  return (
    <FlatGrid
      itemDimension={itemWidth}
      data={documents}
      style={styles.container}
      spacing={itemSpacing}
      renderItem={renderDocument}
      refreshControl={refreshControl}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.contentContainer}
      keyExtractor={item => item.id}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  contentContainer: {
    padding: 16,
  },
});
