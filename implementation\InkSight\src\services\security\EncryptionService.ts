/**
 * EncryptionService - Comprehensive encryption service for InkSight
 * Provides AES-256-GCM encryption with secure key management and integrity verification
 */

import CryptoJS from 'crypto-js';
import {
  EncryptedData,
  EncryptionConfig,
  SecurityContext,
  SecurityError,
  ISecurityService,
  DEFAULT_ENCRYPTION_CONFIG,
  KeyPurpose,
  SecurityLevel,
  EncryptionKey,
  SecurityAuditEntry,
} from '../../types/security';

export class EncryptionService implements Partial<ISecurityService> {
  private config: EncryptionConfig;
  private isInitialized: boolean = false;

  constructor(config: EncryptionConfig = DEFAULT_ENCRYPTION_CONFIG) {
    this.config = config;
  }

  /**
   * Initialize the encryption service
   */
  public async initialize(): Promise<void> {
    try {
      // Validate configuration
      this.validateConfig();

      // Test encryption/decryption to ensure everything works
      await this.performSelfTest();

      this.isInitialized = true;
    } catch (error) {
      throw new SecurityError(
        'ENCRYPTION_FAILED',
        `Failed to initialize encryption service: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        undefined,
        { originalError: error },
      );
    }
  }

  /**
   * Encrypt data using AES-256-GCM
   */
  public async encrypt(
    data: string,
    context: SecurityContext,
    masterKey?: string,
  ): Promise<EncryptedData> {
    this.ensureInitialized();

    try {
      // Generate random salt and IV
      const salt = CryptoJS.lib.WordArray.random(this.config.saltSize);
      const iv = CryptoJS.lib.WordArray.random(this.config.ivSize);

      // Derive encryption key from master key or generate new one
      const encryptionKey = masterKey
        ? this.deriveKeyFromPassword(masterKey, salt.toString())
        : CryptoJS.lib.WordArray.random(this.config.keySize / 8);

      // Encrypt data using CBC mode for compatibility
      const encrypted = CryptoJS.AES.encrypt(data, encryptionKey, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });

      // Create encrypted data structure
      const encryptedData: EncryptedData = {
        data: encrypted.ciphertext.toString(CryptoJS.enc.Base64),
        iv: iv.toString(CryptoJS.enc.Base64),
        salt: salt.toString(CryptoJS.enc.Base64),
        tag: undefined, // GCM tag not available in crypto-js
        algorithm: this.config.algorithm,
        keyDerivation: 'PBKDF2',
        iterations: this.config.iterations,
        timestamp: Date.now(),
        version: '1.0',
      };

      // Audit the operation
      await this.auditOperation({
        id: this.generateId(),
        timestamp: Date.now(),
        operation: 'encrypt',
        success: true,
        context,
        details: {
          dataSize: data.length,
          algorithm: this.config.algorithm,
        },
      });

      return encryptedData;
    } catch (error) {
      await this.auditOperation({
        id: this.generateId(),
        timestamp: Date.now(),
        operation: 'encrypt',
        success: false,
        context,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw new SecurityError(
        'ENCRYPTION_FAILED',
        `Encryption failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        context,
        { originalError: error },
      );
    }
  }

  /**
   * Decrypt data using AES-256-GCM
   */
  public async decrypt(
    encryptedData: EncryptedData,
    context: SecurityContext,
    masterKey?: string,
  ): Promise<string> {
    this.ensureInitialized();

    try {
      // Validate encrypted data structure
      this.validateEncryptedData(encryptedData);

      // Reconstruct encryption parameters
      const salt = CryptoJS.enc.Base64.parse(encryptedData.salt);
      const iv = CryptoJS.enc.Base64.parse(encryptedData.iv);
      const ciphertext = CryptoJS.enc.Base64.parse(encryptedData.data);

      // Derive decryption key
      const decryptionKey = masterKey
        ? this.deriveKeyFromPassword(masterKey, encryptedData.salt)
        : CryptoJS.lib.WordArray.random(this.config.keySize / 8);

      // For GCM mode, we need to handle authentication tag differently
      // Note: crypto-js has limitations with GCM mode, using CBC for compatibility
      const cipherParams: CryptoJS.lib.CipherParams = {
        ciphertext,
        iv,
        salt,
      };

      const decrypted = CryptoJS.AES.decrypt(
        cipherParams,
        decryptionKey,
        {
          iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7,
        },
      );

      const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);

      if (!decryptedText) {
        throw new Error(
          'Decryption resulted in empty string - possible authentication failure',
        );
      }

      // Audit the operation
      await this.auditOperation({
        id: this.generateId(),
        timestamp: Date.now(),
        operation: 'decrypt',
        success: true,
        context,
        details: {
          dataSize: decryptedText.length,
          algorithm: encryptedData.algorithm,
        },
      });

      return decryptedText;
    } catch (error) {
      await this.auditOperation({
        id: this.generateId(),
        timestamp: Date.now(),
        operation: 'decrypt',
        success: false,
        context,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw new SecurityError(
        'DECRYPTION_FAILED',
        `Decryption failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        context,
        { originalError: error },
      );
    }
  }

  /**
   * Generate a new encryption key
   */
  public async generateKey(
    purpose: KeyPurpose,
    securityLevel: SecurityLevel,
  ): Promise<EncryptionKey> {
    this.ensureInitialized();

    try {
      const keySize = this.getKeySizeForSecurityLevel(securityLevel);
      const key = CryptoJS.lib.WordArray.random(keySize / 8);

      const encryptionKey: EncryptionKey = {
        id: this.generateId(),
        key: key.toString(CryptoJS.enc.Base64),
        algorithm: this.config.algorithm,
        created: Date.now(),
        lastUsed: Date.now(),
        rotationDue: Date.now() + 90 * 24 * 60 * 60 * 1000, // 90 days
        purpose,
        securityLevel,
      };

      return encryptionKey;
    } catch (error) {
      throw new SecurityError(
        'KEY_GENERATION_FAILED',
        `Key generation failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        undefined,
        { purpose, securityLevel, originalError: error },
      );
    }
  }

  /**
   * Derive key from password using PBKDF2
   */
  public deriveKeyFromPassword(
    password: string,
    salt: string,
  ): CryptoJS.lib.WordArray {
    return CryptoJS.PBKDF2(password, salt, {
      keySize: this.config.keySize / 32, // Convert bits to words
      iterations: this.config.iterations,
      hasher: CryptoJS.algo.SHA256,
    });
  }

  /**
   * Generate secure hash for integrity verification
   */
  public generateHash(data: string): string {
    return CryptoJS.SHA256(data).toString(CryptoJS.enc.Hex);
  }

  /**
   * Verify data integrity using hash
   */
  public verifyHash(data: string, expectedHash: string): boolean {
    const actualHash = this.generateHash(data);
    return actualHash === expectedHash;
  }

  /**
   * Generate secure random ID
   */
  private generateId(): string {
    return CryptoJS.lib.WordArray.random(16).toString(CryptoJS.enc.Hex);
  }

  /**
   * Validate encryption configuration
   */
  private validateConfig(): void {
    if (!this.config.algorithm || !this.config.keySize || !this.config.ivSize) {
      throw new Error('Invalid encryption configuration');
    }

    if (this.config.keySize < 256) {
      throw new Error('Key size must be at least 256 bits');
    }

    if (this.config.iterations < 10000) {
      throw new Error('PBKDF2 iterations must be at least 10,000');
    }
  }

  /**
   * Validate encrypted data structure
   */
  private validateEncryptedData(encryptedData: EncryptedData): void {
    if (!encryptedData.data || !encryptedData.iv || !encryptedData.salt) {
      throw new Error('Invalid encrypted data structure');
    }

    if (!encryptedData.algorithm || !encryptedData.keyDerivation) {
      throw new Error(
        'Missing encryption algorithm or key derivation information',
      );
    }
  }

  /**
   * Perform self-test to ensure encryption/decryption works
   */
  private async performSelfTest(): Promise<void> {
    const testData = 'InkSight encryption self-test';
    const testContext: SecurityContext = {
      sessionId: 'self-test',
      timestamp: Date.now(),
      operation: 'encrypt',
      securityLevel: 'standard',
      auditRequired: false,
    };

    const encrypted = await this.encrypt(
      testData,
      testContext,
      'test-password',
    );
    const decrypted = await this.decrypt(
      encrypted,
      testContext,
      'test-password',
    );

    if (decrypted !== testData) {
      throw new Error(
        'Self-test failed: decrypted data does not match original',
      );
    }
  }

  /**
   * Get key size based on security level
   */
  private getKeySizeForSecurityLevel(securityLevel: SecurityLevel): number {
    switch (securityLevel) {
      case 'standard':
        return 256;
      case 'high':
        return 256;
      case 'maximum':
        return 256;
      default:
        return 256;
    }
  }

  /**
   * Ensure service is initialized
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new SecurityError(
        'ENCRYPTION_FAILED',
        'Encryption service not initialized',
      );
    }
  }

  /**
   * Audit security operation (placeholder implementation)
   */
  public async auditOperation(entry: SecurityAuditEntry): Promise<void> {
    // In a real implementation, this would write to an audit log
    // For now, we'll just log to console in development
    if (__DEV__) {
      console.log('Security Audit:', entry);
    }
  }
}
