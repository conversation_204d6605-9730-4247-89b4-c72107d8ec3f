{"name": "InkSight", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "format:check": "prettier --check .", "format": "prettier --write .", "type-check": "tsc --noEmit"}, "dependencies": {"@react-navigation/bottom-tabs": "^7.4.0", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.0", "@reduxjs/toolkit": "^2.8.2", "@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0", "react": "18.3.1", "react-native": "0.80.0", "react-native-document-picker": "^9.3.1", "react-native-fs": "^2.20.0", "react-native-keychain": "^10.0.0", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.5.0", "react-native-screens": "^4.11.1", "react-native-sqlite-2": "^3.6.2", "react-native-super-grid": "^6.0.1", "react-redux": "^9.2.0", "expo": ">=51.0.0-0 <52.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.25.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@types/react": "^18.3.12", "@types/react-test-renderer": "^18.3.0", "babel-jest": "^29.7.0", "eslint": "^8.19.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.2", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "npm@10.2.4"}