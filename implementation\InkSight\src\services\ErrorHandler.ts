/**
 * Error Handling Service
 * InkSight - Privacy-first offline e-reader and note-taking app
 */

import { Logger, LogLevel } from './Logger';

// Error types
export enum ErrorType {
  NETWORK = 'network',
  STORAGE = 'storage',
  PARSING = 'parsing',
  HANDWRITING = 'handwriting',
  NAVIGATION = 'navigation',
  PERMISSION = 'permission',
  VALIDATION = 'validation',
  UNKNOWN = 'unknown',
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Error context interface
export interface ErrorContext {
  userId?: string;
  sessionId: string;
  screen?: string;
  action?: string;
  timestamp: string;
  deviceInfo?: {
    platform: string;
    version: string;
    model?: string;
  };
}

// Application error class
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly severity: ErrorSeverity;
  public readonly context: ErrorContext;
  public readonly recoverable: boolean;
  public readonly userMessage: string;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context: Partial<ErrorContext> = {},
    recoverable = true,
    userMessage?: string,
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.severity = severity;
    this.recoverable = recoverable;
    this.userMessage = userMessage || this.getDefaultUserMessage();
    this.context = {
      sessionId: Logger.getSessionId(),
      timestamp: new Date().toISOString(),
      ...context,
    };
  }

  private getDefaultUserMessage(): string {
    switch (this.type) {
      case ErrorType.NETWORK:
        return 'Network connection issue. Please check your connection and try again.';
      case ErrorType.STORAGE:
        return 'Unable to save data. Please check available storage space.';
      case ErrorType.PARSING:
        return 'Unable to process the document. The file may be corrupted.';
      case ErrorType.HANDWRITING:
        return 'Handwriting recognition failed. Please try writing more clearly.';
      case ErrorType.NAVIGATION:
        return 'Navigation error occurred. Please try again.';
      case ErrorType.PERMISSION:
        return 'Permission required to access this feature.';
      case ErrorType.VALIDATION:
        return 'Invalid input provided. Please check your data and try again.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}

// Error recovery strategies
export interface RecoveryStrategy {
  type: string;
  action: () => Promise<boolean>;
  description: string;
}

// Error handler configuration
export interface ErrorHandlerConfig {
  enableGlobalHandler: boolean;
  enableRecovery: boolean;
  maxRetryAttempts: number;
  retryDelay: number;
  enableUserNotification: boolean;
}

class ErrorHandlerService {
  private config: ErrorHandlerConfig;
  private recoveryStrategies: Map<ErrorType, RecoveryStrategy[]> = new Map();
  private errorCounts: Map<string, number> = new Map();

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enableGlobalHandler: true,
      enableRecovery: true,
      maxRetryAttempts: 3,
      retryDelay: 1000,
      enableUserNotification: true,
      ...config,
    };

    this.setupGlobalErrorHandler();
    this.setupRecoveryStrategies();
  }

  // Setup global error handlers
  private setupGlobalErrorHandler(): void {
    if (!this.config.enableGlobalHandler) return;

    // Handle unhandled promise rejections
    interface GlobalWithErrorHandlers extends NodeJS.Global {
      onunhandledrejection?: (event: PromiseRejectionEvent) => void;
    }

    interface PromiseRejectionEvent {
      reason: unknown;
      promise: Promise<unknown>;
    }

    const globalWithHandlers = global as GlobalWithErrorHandlers;
    const originalHandler = globalWithHandlers.onunhandledrejection;

    globalWithHandlers.onunhandledrejection = (event: PromiseRejectionEvent) => {
      this.handleError(
        new AppError(
          `Unhandled Promise Rejection: ${event.reason}`,
          ErrorType.UNKNOWN,
          ErrorSeverity.HIGH,
          { action: 'promise_rejection' },
          false,
        ),
      );

      if (originalHandler) {
        originalHandler(event);
      }
    };

    // Handle JavaScript errors (React Native specific)
    if (typeof ErrorUtils !== 'undefined') {
      const originalErrorHandler = ErrorUtils.getGlobalHandler();
      ErrorUtils.setGlobalHandler((error, isFatal) => {
        this.handleError(
          new AppError(
            error.message,
            ErrorType.UNKNOWN,
            isFatal ? ErrorSeverity.CRITICAL : ErrorSeverity.HIGH,
            { action: 'javascript_error' },
            !isFatal,
          ),
        );

        if (originalErrorHandler) {
          originalErrorHandler(error, isFatal);
        }
      });
    }
  }

  // Setup recovery strategies
  private setupRecoveryStrategies(): void {
    // Storage error recovery
    this.addRecoveryStrategy(ErrorType.STORAGE, {
      type: 'clear_cache',
      action: async () => {
        try {
          // Clear app cache
          Logger.info(
            'Attempting storage recovery: clearing cache',
            'error_handler',
          );
          return true;
        } catch {
          return false;
        }
      },
      description: 'Clear application cache',
    });

    // Network error recovery
    this.addRecoveryStrategy(ErrorType.NETWORK, {
      type: 'retry_connection',
      action: async () => {
        try {
          // Since we're offline-first, this would check local connectivity
          Logger.info(
            'Attempting network recovery: checking connectivity',
            'error_handler',
          );
          return true;
        } catch {
          return false;
        }
      },
      description: 'Retry network connection',
    });

    // Handwriting error recovery
    this.addRecoveryStrategy(ErrorType.HANDWRITING, {
      type: 'reset_recognition',
      action: async () => {
        try {
          Logger.info(
            'Attempting handwriting recovery: resetting recognition',
            'error_handler',
          );
          // Reset handwriting recognition state
          return true;
        } catch {
          return false;
        }
      },
      description: 'Reset handwriting recognition',
    });
  }

  // Main error handling method
  async handleError(error: Error | AppError): Promise<void> {
    const appError =
      error instanceof AppError ? error : this.convertToAppError(error);

    // Log the error
    this.logError(appError);

    // Track error frequency
    this.trackErrorFrequency(appError);

    // Attempt recovery if enabled and error is recoverable
    if (this.config.enableRecovery && appError.recoverable) {
      const recovered = await this.attemptRecovery(appError);
      if (recovered) {
        Logger.info('Error recovery successful', 'error_handler', {
          errorType: appError.type,
          errorMessage: appError.message,
        });
        return;
      }
    }

    // Notify user if enabled
    if (this.config.enableUserNotification) {
      this.notifyUser(appError);
    }
  }

  // Convert regular Error to AppError
  private convertToAppError(error: Error): AppError {
    return new AppError(
      error.message,
      ErrorType.UNKNOWN,
      ErrorSeverity.MEDIUM,
      { action: 'converted_error' },
      true,
    );
  }

  // Log error with appropriate level
  private logError(error: AppError): void {
    const logLevel = this.getLogLevel(error.severity);
    const logData = {
      type: error.type,
      severity: error.severity,
      recoverable: error.recoverable,
      context: error.context,
    };

    switch (logLevel) {
      case LogLevel.ERROR:
        Logger.error(error.message, 'error_handler', logData, error);
        break;
      case LogLevel.FATAL:
        Logger.fatal(error.message, 'error_handler', logData, error);
        break;
      default:
        Logger.warn(error.message, 'error_handler', logData);
    }
  }

  // Get appropriate log level for error severity
  private getLogLevel(severity: ErrorSeverity): LogLevel {
    switch (severity) {
      case ErrorSeverity.LOW:
        return LogLevel.WARN;
      case ErrorSeverity.MEDIUM:
        return LogLevel.ERROR;
      case ErrorSeverity.HIGH:
        return LogLevel.ERROR;
      case ErrorSeverity.CRITICAL:
        return LogLevel.FATAL;
      default:
        return LogLevel.ERROR;
    }
  }

  // Track error frequency for pattern detection
  private trackErrorFrequency(error: AppError): void {
    const key = `${error.type}_${error.message}`;
    const count = this.errorCounts.get(key) || 0;
    this.errorCounts.set(key, count + 1);

    // Log if error is becoming frequent
    if (count > 5) {
      Logger.warn('Frequent error detected', 'error_handler', {
        errorType: error.type,
        errorMessage: error.message,
        count: count + 1,
      });
    }
  }

  // Attempt error recovery
  private async attemptRecovery(error: AppError): Promise<boolean> {
    const strategies = this.recoveryStrategies.get(error.type);
    if (!strategies || strategies.length === 0) return false;

    for (const strategy of strategies) {
      try {
        Logger.info(
          `Attempting recovery strategy: ${strategy.type}`,
          'error_handler',
        );
        const success = await strategy.action();
        if (success) {
          Logger.info(
            `Recovery strategy successful: ${strategy.type}`,
            'error_handler',
          );
          return true;
        }
      } catch (recoveryError) {
        Logger.error(
          `Recovery strategy failed: ${strategy.type}`,
          'error_handler',
          { strategy: strategy.type },
          recoveryError as Error,
        );
      }
    }

    return false;
  }

  // Notify user about error (placeholder for UI integration)
  private notifyUser(error: AppError): void {
    // This would integrate with a notification system or error display component
    Logger.info('User notification triggered', 'error_handler', {
      userMessage: error.userMessage,
      severity: error.severity,
    });
  }

  // Add recovery strategy
  addRecoveryStrategy(errorType: ErrorType, strategy: RecoveryStrategy): void {
    const strategies = this.recoveryStrategies.get(errorType) || [];
    strategies.push(strategy);
    this.recoveryStrategies.set(errorType, strategies);
  }

  // Get error statistics
  getErrorStats(): {
    totalErrors: number;
    errorsByType: Record<ErrorType, number>;
    errorsBySeverity: Record<ErrorSeverity, number>;
    frequentErrors: Array<{ error: string; count: number }>;
  } {
    const errorsByType = {} as Record<ErrorType, number>;
    const errorsBySeverity = {} as Record<ErrorSeverity, number>;

    // This would be populated from logged errors
    const frequentErrors = Array.from(this.errorCounts.entries())
      .map(([error, count]) => ({ error, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalErrors: Array.from(this.errorCounts.values()).reduce(
        (sum, count) => sum + count,
        0,
      ),
      errorsByType,
      errorsBySeverity,
      frequentErrors,
    };
  }

  // Clear error tracking data
  clearErrorData(): void {
    this.errorCounts.clear();
    Logger.info('Error tracking data cleared', 'error_handler');
  }
}

// Create and export singleton instance
export const ErrorHandler = new ErrorHandlerService();

// Export convenience functions
export const handleError = (error: Error | AppError) =>
  ErrorHandler.handleError(error);

export const createError = (
  message: string,
  type: ErrorType = ErrorType.UNKNOWN,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
  context: Partial<ErrorContext> = {},
  recoverable = true,
  userMessage?: string,
) => new AppError(message, type, severity, context, recoverable, userMessage);

export default ErrorHandler;
